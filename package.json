{"name": "<PERSON><PERSON><PERSON>", "version": "2.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular-material-components/file-input": "^16.0.1", "@angular/animations": "^18.0.3", "@angular/cdk": "^18.0.3", "@angular/common": "^18.0.3", "@angular/compiler": "^18.0.3", "@angular/core": "^18.0.3", "@angular/forms": "^18.0.3", "@angular/material": "^18.0.3", "@angular/platform-browser": "^18.0.3", "@angular/platform-browser-dynamic": "^18.0.3", "@angular/router": "^18.0.3", "@kolkov/angular-editor": "^2.1.0", "@ng-matero/extensions": "^17.0.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@types/lodash": "^4.17.13", "angular-calendar": "^0.29.0", "angular-tabler-icons": "^2.7.0", "apexcharts": "^3.52.0", "bootstrap": "^5.3.3", "date-fns": "^2.29.3", "dayjs": "^1.11.13", "dropzone": "^6.0.0-beta.2", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-stat": "^1.2.0", "iconify": "^1.4.0", "lodash": "^4.17.21", "moment": "^2.30.1", "ng-apexcharts": "^1.7.6", "ng2-search-filter": "^0.5.1", "ngx-daterangepicker-material": "^6.0.4", "ngx-dropzone-wrapper": "^17.0.0", "ngx-echarts": "^18.0.0", "ngx-owl-carousel-o": "^19.0.2", "ngx-pagination": "^6.0.1", "ngx-permissions": "^13.0.1", "ngx-scrollbar": "^11.0.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.5.0", "sass": "^1.57.1", "swiper": "^11.1.15", "tslib": "^2.3.0", "uuid": "^11.0.3", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.4", "@angular/cli": "~18.0.4", "@angular/compiler-cli": "^18.0.3", "@types/date-fns": "^2.6.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.4.5"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}