import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialog,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { TextInputComponent } from '../form-controls/text-input/text-input.component';
import { PhoneInputComponent } from '../form-controls/phone-input/phone-input.component';
import {
  SelectInputComponent,
  SelectOption,
} from '../form-controls/select-input/select-input.component';
import { DatetimeInputComponent } from '../form-controls/datetime-input/datetime-input.component';
import { NumberInputComponent } from '../form-controls/number-input/number-input.component';
import {
  BookingService,
  BookingFormData,
  BranchOption,
  MealOption,
} from '../../../core/services/booking.service';
import { BranchStore, MenuService } from '../../../core/services/menu.service';
import {
  BookingResultDialogComponent,
  BookingResultData,
} from '../booking-result-dialog/booking-result-dialog.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-booking-dialog',
  templateUrl: './booking-dialog.component.html',
  styleUrls: ['./booking-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TextInputComponent,
    PhoneInputComponent,
    SelectInputComponent,
    DatetimeInputComponent,
    NumberInputComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BookingDialogComponent implements OnInit, OnDestroy {
  bookingForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;

  branchOptions: SelectOption[] = [];
  mealTypeOptions: SelectOption[] = [];
  branches: BranchStore[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<BookingDialogComponent>,
    private bookingService: BookingService,
    private menuService: MenuService,
    private dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
    // Đảm bảo carousel bị vô hiệu hóa khi dialog mở
    this.disableCarouselCompletely();
  }

  ngOnDestroy(): void {
    // Enable lại carousel khi dialog bị destroy
    this.enableCarouselCompletely();
  }

  // Vô hiệu hóa hoàn toàn carousel
  private disableCarouselCompletely(): void {
    const carousel = document.getElementById('homeCarousel');
    if (carousel) {
      // Disable pointer events
      carousel.style.pointerEvents = 'none';

      // Pause carousel
      if ((window as any).bootstrap && (window as any).bootstrap.Carousel) {
        const carouselInstance = (window as any).bootstrap.Carousel.getOrCreateInstance(carousel);
        carouselInstance.pause();
      }

      // Disable tất cả controls
      const controls = carousel.querySelectorAll(
        '.carousel-control-prev, .carousel-control-next, .carousel-indicators button',
      );
      controls.forEach(control => {
        (control as HTMLElement).style.pointerEvents = 'none';
        control.setAttribute('disabled', 'true');
      });

      console.log('🔥 Carousel completely disabled from booking dialog');
    }
  }

  // Enable lại carousel
  private enableCarouselCompletely(): void {
    const carousel = document.getElementById('homeCarousel');
    if (carousel) {
      // Enable pointer events
      carousel.style.pointerEvents = 'auto';

      // Resume carousel
      if ((window as any).bootstrap && (window as any).bootstrap.Carousel) {
        const carouselInstance = (window as any).bootstrap.Carousel.getOrCreateInstance(carousel);
        carouselInstance.cycle();
      }

      // Enable tất cả controls
      const controls = carousel.querySelectorAll(
        '.carousel-control-prev, .carousel-control-next, .carousel-indicators button',
      );
      controls.forEach(control => {
        (control as HTMLElement).style.pointerEvents = 'auto';
        control.removeAttribute('disabled');
      });

      console.log('🔥 Carousel completely enabled from booking dialog');
    }
  }

  private initializeForm(): void {
    this.bookingForm = this.fb.group({
      branchId: ['', [Validators.required]],
      customerName: ['', [Validators.required, Validators.minLength(2)]],
      phoneNumber: [
        '',
        [Validators.required, Validators.pattern(/^[0-9]{10}$/), Validators.maxLength(10)],
      ],
      email: ['', [Validators.email]],
      reservationDate: ['', [Validators.required]],
      adultCount: [1, [Validators.required, Validators.min(1)]],
      childCount: [0, [Validators.min(0)]],
      mealType: ['', [Validators.required]],
      notes: [''],
    });
  }

  private loadInitialData(): void {
    this.isLoading = true;

    // Load branches từ API
    this.bookingService.getBranches().subscribe({
      next: (branchOptions: BranchOption[]) => {
        this.branchOptions = branchOptions.map(branch => ({
          value: branch.value,
          label: branch.label,
        }));

        // Load chi tiết branches để sử dụng khi submit
        this.loadBranchDetails();
      },
      error: error => {
        console.error('Error loading branches:', error);
        this.isLoading = false;
        this.showErrorDialog(
          'Lỗi tải dữ liệu',
          'Không thể tải danh sách chi nhánh. Vui lòng thử lại.',
        );
      },
    });

    // Load meal types
    this.bookingService.getMealTypes().subscribe({
      next: (mealTypes: MealOption[]) => {
        this.mealTypeOptions = mealTypes.map(meal => ({
          value: meal.value,
          label: meal.label,
        }));
        this.isLoading = false;
      },
      error: error => {
        console.error('Error loading meal types:', error);
        this.isLoading = false;
        this.showErrorDialog(
          'Lỗi tải dữ liệu',
          'Không thể tải danh sách suất ăn. Vui lòng thử lại.',
        );
      },
    });
  }

  private loadBranchDetails(): void {
    // Load chi tiết branches từ MenuService để có đầy đủ thông tin
    this.menuService.getBranchStores(environment.storeId).subscribe({
      next: response => {
        if (response && response.data && response.data.branchStore) {
          this.branches = response.data.branchStore;
        }
      },
      error: error => {
        console.error('Error loading branch details:', error);
      },
    });
  }

  onSubmit(): void {
    console.log('🔥 Form submission attempt');
    console.log('🔥 Form valid:', this.bookingForm.valid);
    console.log('🔥 Form value:', this.bookingForm.value);
    console.log('🔥 Form errors:', this.getFormErrors());

    if (this.bookingForm.valid) {
      this.isSubmitting = true;

      const formValue = this.bookingForm.value;

      // Tìm branch được chọn
      const selectedBranch = this.branches.find(branch => branch._id === formValue.branchId);

      if (!selectedBranch) {
        this.isSubmitting = false;
        this.showErrorDialog(
          'Lỗi dữ liệu',
          'Không tìm thấy thông tin chi nhánh. Vui lòng thử lại.',
        );
        return;
      }

      const bookingData: BookingFormData = {
        branchId: formValue.branchId,
        customerName: formValue.customerName,
        phoneNumber: formValue.phoneNumber,
        email: formValue.email,
        reservationDate: formValue.reservationDate,
        adultCount: formValue.adultCount,
        childCount: formValue.childCount,
        mealType: formValue.mealType,
        notes: formValue.notes,
      };

      console.log('🔥 Booking form data:', bookingData);
      console.log('🔥 Selected branch:', selectedBranch);

      this.bookingService.createBooking(bookingData, selectedBranch).subscribe({
        next: response => {
          this.isSubmitting = false;
          console.log('🔥 API Response:', response);

          // API trả về error: false khi thành công, không phải success: true
          if (!response.error && response.data) {
            // Transform API response data to match template expectations
            const apiData = response.data as any; // Type assertion to access dynamic properties
            const transformedBookingData = {
              customerName: formValue.customerName,
              phoneNumber: formValue.phoneNumber || apiData.phone,
              email: formValue.email,
              reservationDate: apiData.items?.[0]?.reservationDate || formValue.reservationDate,
              reservationTime: apiData.items?.[0]?.reservationTime,
              adultCount: apiData.items?.[0]?.numberOfGuests || formValue.adultCount,
              childCount: formValue.childCount,
              branchName: apiData.branchName,
              branchAddress: apiData.branchAddress,
              orderId: apiData.orderId,
              notes: formValue.notes,
            };

            this.showSuccessDialog(
              'Đặt bàn thành công!',
              response.message || 'Thông tin đặt bàn của bạn đã được ghi nhận.',
              transformedBookingData,
            );
            this.dialogRef.close(response.data);
          } else {
            this.showErrorDialog(
              'Lỗi đặt bàn',
              response.message || 'Không thể hoàn tất đặt bàn. Vui lòng thử lại.',
            );
          }
        },
        error: error => {
          this.isSubmitting = false;
          console.error('Booking error:', error);

          let errorMessage = 'Có lỗi xảy ra khi đặt bàn';
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }
          this.showErrorDialog('Lỗi đặt bàn', errorMessage);
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.bookingForm.controls).forEach(key => {
      const control = this.bookingForm.get(key);
      control?.markAsTouched();
    });
  }

  private getFormErrors(): any {
    const errors: any = {};
    Object.keys(this.bookingForm.controls).forEach(key => {
      const control = this.bookingForm.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  get minDate(): Date {
    return new Date();
  }

  private showSuccessDialog(title: string, message: string, bookingData?: any): void {
    const dialogData: BookingResultData = {
      success: true,
      title,
      message,
      bookingData,
    };

    this.dialog.open(BookingResultDialogComponent, {
      width: '400px',
      data: dialogData,
      disableClose: false,
    });
  }

  private showErrorDialog(title: string, message: string, errorDetails?: string): void {
    const dialogData: BookingResultData = {
      success: false,
      title,
      message,
      errorDetails,
    };

    this.dialog.open(BookingResultDialogComponent, {
      width: '400px',
      data: dialogData,
      disableClose: false,
    });
  }
}
