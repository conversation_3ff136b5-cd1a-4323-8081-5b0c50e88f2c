import { Component, Input, OnInit, Optional, Self } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import moment from 'moment';
import dayjs from 'dayjs';
import { BaseFormControlComponent } from '../base-form-control.component';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';

@Component({
  selector: 'app-datetime-input',
  templateUrl: './datetime-input.component.html',
  styleUrls: ['./datetime-input.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    NgxDaterangepickerMd,
  ],
})
export class DatetimeInputComponent extends BaseFormControlComponent implements OnInit {
  @Input() minDate?: Date | moment.Moment;
  @Input() maxDate?: Date | moment.Moment;
  @Input() prefixIcon = '';
  @Input() suffixIcon = '';
  @Input() timePicker = true;
  @Input() timePicker24Hour = true;
  @Input() timePickerIncrement = 15;
  @Input() singleDatePicker = true;
  @Input() showDropdowns = true;
  @Input() showWeekNumbers = false;
  @Input() showISOWeekNumbers = false;
  @Input() ranges: any = {};
  @Input() showClearButton = true;
  @Input() showCancel = true;
  @Input() linkedCalendars = false;
  @Input() alwaysShowCalendars = false;

  // Locale configuration
  locale: any = {
    format: 'DD/MM/YYYY HH:mm',
    displayFormat: 'DD/MM/YYYY HH:mm',
    direction: 'ltr',
    weekLabel: 'W',
    separator: ' - ',
    cancelLabel: 'Hủy',
    applyLabel: 'Áp dụng',
    clearLabel: 'Xóa',
    customRangeLabel: 'Tùy chọn',
    daysOfWeek: moment.weekdaysMin(),
    monthNames: moment.monthsShort(),
    firstDay: moment.localeData().firstDayOfWeek(),
  };

  constructor(@Optional() @Self() public override ngControl: NgControl) {
    super(ngControl);
  }

  override ngOnInit(): void {
    super.ngOnInit();

    // Set default error messages if not provided
    if (Object.keys(this.errorMessages).length === 0) {
      this.errorMessages = {
        required: 'Vui lòng chọn ngày giờ',
        min: 'Ngày giờ không được nhỏ hơn thời gian tối thiểu',
        max: 'Ngày giờ không được lớn hơn thời gian tối đa',
      };
    }

    // Set minimum date to today if not provided
    if (!this.minDate) {
      this.minDate = moment().toDate();
    }
  }

  get selectedDate(): moment.Moment | null {
    const value = this.formControl.value;
    if (value) {
      if (moment.isMoment(value)) {
        return value;
      }
      if (typeof value === 'string' && value.trim()) {
        try {
          const parsed = moment(value, this.locale.format, true); // strict parsing
          return parsed.isValid() ? parsed : null;
        } catch (error) {
          console.warn('Error parsing date:', value, error);
          return null;
        }
      }
      if (value instanceof Date) {
        return moment(value);
      }
    }
    return null;
  }

  get displayValue(): string {
    const value = this.formControl.value;
    if (typeof value === 'string' && value.trim()) {
      return value; // Return string value directly to avoid parsing loop
    }
    const selected = this.selectedDate;
    return selected ? selected.format(this.locale.displayFormat) : '';
  }

  get minDateValue(): dayjs.Dayjs | undefined {
    if (!this.minDate) return undefined;
    if (this.minDate instanceof Date) {
      return dayjs(this.minDate);
    }
    if (moment.isMoment(this.minDate)) {
      return dayjs(this.minDate.toDate());
    }
    return undefined;
  }

  get maxDateValue(): dayjs.Dayjs | undefined {
    if (!this.maxDate) return undefined;
    if (this.maxDate instanceof Date) {
      return dayjs(this.maxDate);
    }
    if (moment.isMoment(this.maxDate)) {
      return dayjs(this.maxDate.toDate());
    }
    return undefined;
  }

  onDateSelected(event: any): void {
    if (event && event.startDate) {
      // Store the moment object directly for better handling
      const selectedMoment = moment(event.startDate);

      // Format as string datetime for formControl if needed
      if (this.singleDatePicker) {
        const dateTimeString = selectedMoment.format(this.locale.format);
        console.log('🔥 DateTime selected:', dateTimeString);

        // Set value and trigger change
        this.writeValue(dateTimeString);
        this.triggerChange(dateTimeString);

        console.log('🔥 Form control value after set:', this.formControl.value);
        console.log('🔥 Form control valid:', this.formControl.valid);
        console.log('🔥 Form control errors:', this.formControl.errors);
      } else {
        // For date range, store the range object
        const rangeValue = {
          startDate: event.startDate,
          endDate: event.endDate,
        };
        this.writeValue(rangeValue);
        this.triggerChange(rangeValue);
      }

      this.markAsTouched();
      this.onTouched();
    }
  }

  onCancel(): void {
    // Reset to previous value or empty
    this.onTouched();
  }

  onClear(): void {
    this.writeValue(null);
    this.triggerChange(null);
    this.markAsTouched();
    this.onTouched();
  }

  onShow() {
    console.log('🔥 DateTime picker shown');
    // Disable carousel events khi datetime picker mở
    this.disableCarouselEvents();
  }

  onHide() {
    console.log('🔥 DateTime picker hidden');
    // Enable lại carousel events khi datetime picker đóng
    this.enableCarouselEvents();
  }

  // Helper methods để disable/enable carousel events
  private disableCarouselEvents() {
    const carousel = document.getElementById('homeCarousel');
    if (carousel) {
      carousel.style.pointerEvents = 'none';
      console.log('🔥 Carousel events disabled from datetime picker');
    }
  }

  private enableCarouselEvents() {
    const carousel = document.getElementById('homeCarousel');
    if (carousel) {
      carousel.style.pointerEvents = 'auto';
      console.log('🔥 Carousel events enabled from datetime picker');
    }
  }

  shouldShowError(): boolean {
    // Check both internal form control and ngControl from parent
    const internalInvalid =
      this.formControl.invalid && (this.formControl.touched || this.formControl.dirty);
    const parentInvalid =
      this.ngControl &&
      this.ngControl.control &&
      this.ngControl.control.invalid &&
      (this.ngControl.control.touched || this.ngControl.control.dirty);

    console.log('🔥 shouldShowError check:', {
      internalInvalid,
      parentInvalid,
      formControlValue: this.formControl.value,
      formControlValid: this.formControl.valid,
      formControlTouched: this.formControl.touched,
      ngControlValue: this.ngControl?.control?.value,
      ngControlValid: this.ngControl?.control?.valid,
      ngControlTouched: this.ngControl?.control?.touched,
    });

    return internalInvalid || !!parentInvalid;
  }

  // Override writeValue to handle different value types
  override writeValue(value: any): void {
    // Avoid setting the same value to prevent infinite loops
    if (this.formControl.value === value) {
      return;
    }

    if (value !== undefined && value !== null) {
      // Handle different input value types
      if (moment.isMoment(value)) {
        const formatted = value.format(this.locale.format);
        this.formControl.setValue(formatted, { emitEvent: false });
      } else if (typeof value === 'string' && value.trim()) {
        // For string values, set directly without re-parsing to avoid loops
        this.formControl.setValue(value, { emitEvent: false });
      } else if (value instanceof Date) {
        const momentValue = moment(value);
        const formatted = momentValue.format(this.locale.format);
        this.formControl.setValue(formatted, { emitEvent: false });
      } else if (typeof value === 'object' && value.startDate && value.endDate) {
        // Handle date range object
        this.formControl.setValue(value, { emitEvent: false });
      } else {
        this.formControl.setValue(null, { emitEvent: false });
      }
    } else {
      this.formControl.setValue(null, { emitEvent: false });
    }
  }
}
