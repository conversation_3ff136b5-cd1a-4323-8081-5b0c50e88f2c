<mat-label *ngIf="label">{{ label }}</mat-label>
<mat-form-field appearance="outline" class="w-100">
  <mat-icon *ngIf="prefixIcon" matPrefix>{{ prefixIcon }}</mat-icon>

  <input
    matInput
    ngxDaterangepickerMd
    [placeholder]="placeholder || 'Chọn ngày giờ'"
    [disabled]="isDisabled"
    [formControl]="formControl"
    [required]="required"
    [value]="displayValue"
    [singleDatePicker]="singleDatePicker"
    [autoApply]="true"
    [timePicker]="timePicker"
    [timePicker24Hour]="timePicker24Hour"
    [timePickerIncrement]="timePickerIncrement"
    [showDropdowns]="showDropdowns"
    [showWeekNumbers]="showWeekNumbers"
    [showISOWeekNumbers]="showISOWeekNumbers"
    [ranges]="ranges"
    [showClearButton]="showClearButton"
    [showCancel]="showCancel"
    [linkedCalendars]="linkedCalendars"
    [alwaysShowCalendars]="alwaysShowCalendars"
    [locale]="locale"
    [drops]="'down'"
    [opens]="'right'"
    [closeOnAutoApply]="false"
    (datesUpdated)="onDateSelected($event)"
    (cancelDaterangepicker)="onCancel()"
    (clearDaterangepicker)="onClear()"
    (showDaterangepicker)="onShow()"
    (hideDaterangepicker)="onHide()"
    (blur)="markAsTouched()"
    (focus)="markAsTouched()"
    readonly
  />

  <mat-icon *ngIf="suffixIcon" matSuffix>{{ suffixIcon }}</mat-icon>

  <mat-hint *ngIf="hint">{{ hint }}</mat-hint>

  <mat-error *ngIf="formControl.invalid && formControl.touched">
    {{ getErrorMessage() }}
  </mat-error>
</mat-form-field>
