:host {
  display: block;
  width: 100%;
  ::ng-deep .mat-mdc-form-field {
    margin-bottom: 0;

    .mat-mdc-text-field-wrapper {
      &:hover {
        border-color: #cbd5e1;
        background-color: #f1f5f9;
      }

      &.mdc-text-field--focused {
        border-color: #2563eb;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }

      // Error state styling
      &.mdc-text-field--invalid {
        border-color: #ef4444 !important;

        &:hover {
          border-color: #dc2626 !important;
        }
      }
    }

    // Error state for the entire form field
    &.mat-form-field-invalid {
      .mat-mdc-text-field-wrapper {
        border-color: #ef4444 !important;
      }
    }

    .mat-mdc-form-field-flex {
      height: 48px;
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      padding: 0;
      min-height: auto;

      .mat-mdc-select {
        font-size: 15px;
        font-weight: 500;
        color: #1f2937;
      }
    }

    .mat-mdc-form-field-icon-prefix,
    .mat-mdc-form-field-icon-suffix {
      color: #6b7280;
      margin: 0 8px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      .mat-mdc-form-field-hint,
      .mat-mdc-form-field-error {
        font-size: 13px;
        font-weight: 500;
      }

      .mat-mdc-form-field-error {
        color: #ef4444;
      }

      .mat-mdc-form-field-hint {
        color: #6b7280;
      }
    }

    .mdc-floating-label {
      font-size: 14px;
      font-weight: 600;
      color: #374151;

      &.mdc-floating-label--float-above {
        color: #2563eb;
      }
    }
  }
}

.w-100 {
  width: 100%;
}

.prefix-icon, .suffix-icon {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.54);
  margin-right: 8px;
}

.suffix-icon {
  margin-right: 0;
  margin-left: 8px;
}

.mat-icon {
  padding: 0!important;
}


// Input styling for daterangepicker
input[ngxDaterangepickerMd] {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  cursor: pointer;

  &:focus {
    cursor: text;
  }

  &::placeholder {
    color: #9ca3af;
    font-weight: 400;
  }
}

::ng-deep .md-drppicker {
  top: 65px!important;
}
