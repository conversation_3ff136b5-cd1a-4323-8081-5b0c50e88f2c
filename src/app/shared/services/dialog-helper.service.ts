import { Injectable } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ComponentType } from '@angular/cdk/portal';
import { Observable } from 'rxjs';
import { Overlay } from '@angular/cdk/overlay';

@Injectable({
  providedIn: 'root',
})
export class DialogHelperService {
  constructor(private dialog: MatDialog, private overlay: Overlay) {}

  /**
   * Mở dialog với cấu hình responsive
   * @param component Component cần mở
   * @param config C<PERSON><PERSON> hình bổ sung (optional)
   * @returns Observable của dialog result
   */
  openResponsiveDialog<T, D = any, R = any>(
    component: ComponentType<T>,
    config?: Partial<MatDialogConfig<D>>,
  ): Observable<R> {
    const isMobile = this.isMobileDevice();

    const defaultConfig: MatDialogConfig<D> = {
      disableClose: false,
      autoFocus: true,
      panelClass: isMobile ? 'booking-dialog-container-mobile' : 'booking-dialog-container',
      scrollStrategy: this.overlay.scrollStrategies.noop(), // Prevent adding cdk-global-scrollblock
      ...(isMobile
        ? {
            width: '100vw',
            maxWidth: '100vw',
            height: '90vh',
            maxHeight: '90vh',
          }
        : {
            width: '90%',
            maxWidth: '700px',
            minWidth: '320px',
            maxHeight: '85vh', // Remove fixed height, let content determine height
          }),
    };

    const finalConfig = { ...defaultConfig, ...config };
    const dialogRef = this.dialog.open(component, finalConfig);

    return dialogRef.afterClosed();
  }

  /**
   * Kiểm tra xem có phải mobile device không
   * @returns true nếu là mobile
   */
  private isMobileDevice(): boolean {
    return window.innerWidth < 768;
  }

  /**
   * Mở booking dialog với cấu hình responsive
   * @param component BookingDialogComponent
   * @param data Data truyền vào dialog (optional)
   * @returns Observable của dialog result
   */
  openBookingDialog<T, D = any, R = any>(component: ComponentType<T>, data?: D): Observable<R> {
    return this.openResponsiveDialog(component, { data });
  }
}
