import {
  Component,
  ViewEncapsulation,
  HostBinding,
  OnInit,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { BannerService, Banner } from '../../core/services/banner.service';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../components/loading/loading.service';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'home-export',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule, CarouselModule],
  templateUrl: './HomeExport.component.html',
  styleUrls: ['./HomeExport.component.scss'],
})
export class HomeExport implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';
  @ViewChild('owlCarousel') owlCarousel: any;

  banners: Banner[] = [];
  categoryHomeBanners: Banner[] = [];
  isLoading: boolean = true;
  isCategoryHomeLoading: boolean = true;
  error: string | null = null;
  categoryHomeError: string | null = null;
  private subscription: Subscription = new Subscription();

  // Owl Carousel options
  carouselOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: false,
    dots: false,
    navSpeed: 700,
    navText: ['', ''],
    autoplay: true,
    autoplayTimeout: 4000,
    autoplayHoverPause: true,
    responsive: {
      0: {
        items: 1,
      },
      400: {
        items: 1,
      },
      740: {
        items: 1,
      },
      940: {
        items: 1,
      },
    },
    nav: false,
  };

  constructor(
    private bannerService: BannerService,
    private loadingService: LoadingService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.loadBanners();
    this.loadCategoryHomeBanners();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscription.unsubscribe();
  }

  // TrackBy function for better performance
  trackByBanner(index: number, banner: Banner): any {
    return banner._id || index;
  }

  // Pause carousel (called from navbar when booking dialog opens)
  pauseCarousel(): void {
    if (this.owlCarousel && this.owlCarousel.stop) {
      this.owlCarousel.stop();
      console.log('🔥 Owl Carousel paused');
    }
  }

  // Resume carousel (called from navbar when booking dialog closes)
  resumeCarousel(): void {
    if (this.owlCarousel && this.owlCarousel.play) {
      this.owlCarousel.play();
      console.log('🔥 Owl Carousel resumed');
    }
  }

  loadBanners(): void {
    this.isLoading = true;
    this.error = null;

    // Show global loading indicator
    this.loadingService.show();

    const bannerSub = this.bannerService.getBanners().subscribe({
      next: response => {
        this.isLoading = false;
        // Hide global loading indicator
        this.loadingService.hide();

        if (!response.error && response.data.banners) {
          this.banners = response.data.banners;
          console.log('Banners loaded:', this.banners);
          // Owl Carousel sẽ tự động xử lý autoplay
        } else {
          this.error = response.message || 'Không thể tải banner';
          console.error('Error in banner response:', response);
        }
      },
      error: err => {
        this.isLoading = false;
        // Hide global loading indicator on error
        this.loadingService.hide();

        this.error = 'Đã xảy ra lỗi khi tải banner';
        console.error('Banner loading error:', err);
      },
    });

    this.subscription.add(bannerSub);
  }

  loadCategoryHomeBanners(): void {
    this.isCategoryHomeLoading = true;
    this.categoryHomeError = null;

    const categoryHomeSub = this.bannerService.getBannerStores('category_home').subscribe({
      next: response => {
        this.isCategoryHomeLoading = false;

        if (!response.error && response.data.banners) {
          this.categoryHomeBanners = response.data.banners;
          console.log('Category home banners loaded:', this.categoryHomeBanners);
        } else {
          this.categoryHomeError = response.message || 'Không thể tải banner category home';
          console.error('Error in category home banner response:', response);
        }
      },
      error: err => {
        this.isCategoryHomeLoading = false;
        this.categoryHomeError = 'Đã xảy ra lỗi khi tải banner category home';
        console.error('Category home banner loading error:', err);
      },
    });

    this.subscription.add(categoryHomeSub);
  }

  getBannerImageUrl(banner: Banner): string {
    // Use Google Cloud Storage URL as the base for banner images
    return banner.thumbail.startsWith('http')
      ? encodeURIComponent(banner.thumbail)
      : `${environment.imageApiUrl}/${encodeURIComponent(banner.thumbail)}`;
  }

  onImageClick(banner: Banner) {
    // Handle banner click based on screen type
    console.log('Banner clicked:', banner);

    // Basic navigation logic based on banner screen type
    if (banner.screen === 'LINK' && banner.params && banner.params.startsWith('http')) {
      window.open(banner.params, '_blank');
    } else if (banner.screen === 'LINK' && banner.params && !banner.params.startsWith('http')) {
      this.router.navigate([banner.params]);
    }

    // Additional navigation logic can be added here
  }

  // Các methods cũ đã được thay thế bởi Owl Carousel
  // Owl Carousel tự động xử lý autoplay, pause, resume

  resumeAutoSlide(): void {
    // Owl Carousel tự động xử lý resume khi hover out
    this.resumeCarousel();
  }
}
