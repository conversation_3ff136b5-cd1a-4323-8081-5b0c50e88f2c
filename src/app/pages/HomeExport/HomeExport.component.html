<!-- Owl Carousel Slider - Full Width -->
<div id="homeCarousel" class="owl-carousel-container full-width-carousel">
  <!-- Loading handled by global loading service -->

  <!-- Error State -->
  <div *ngIf="!isLoading && error" class="alert alert-danger m-3">
    {{ error }}
  </div>

  <!-- Carousel Content when data loaded -->
  <ng-container *ngIf="!isLoading && !error && banners.length > 0">
    <owl-carousel-o #owlCarousel [options]="carouselOptions" class="owl-carousel-home">
      <ng-container *ngFor="let banner of banners; trackBy: trackByBanner">
        <ng-template carouselSlide>
          <div class="carousel-slide" (click)="onImageClick(banner)">
            <img
              [src]="getBannerImageUrl(banner)"
              class="carousel-image"
              [alt]="banner.name"
              style="cursor: pointer"
            />
            <div class="carousel-caption d-none d-md-block">
              <!-- <h5>{{ banner.name }}</h5> -->
            </div>
          </div>
        </ng-template>
      </ng-container>
    </owl-carousel-o>
  </ng-container>

  <!-- No banners state -->
  <div *ngIf="!isLoading && !error && banners.length === 0" class="text-center p-5">
    <p>Không có banner nào.</p>
  </div>
</div>

<div class="home-export">
  <div class="container">
    <!-- Loading State -->
    <div *ngIf="isCategoryHomeLoading" class="text-center p-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Đang tải...</span>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="!isCategoryHomeLoading && categoryHomeError" class="alert alert-danger">
      {{ categoryHomeError }}
    </div>

    <!-- Category Home Banners -->
    <div *ngIf="!isCategoryHomeLoading && !categoryHomeError" class="row g-4">
      <div
        *ngFor="let banner of categoryHomeBanners; let i = index"
        class="col-md-6"
        [class.col-md-12]="categoryHomeBanners.length === 1"
        [class.col-md-4]="categoryHomeBanners.length === 3"
        [class.col-md-3]="categoryHomeBanners.length === 4"
      >
        <div class="seafood-content" (click)="onImageClick(banner)" style="cursor: pointer">
          <img
            [src]="getBannerImageUrl(banner)"
            [alt]="banner.name"
            class="seafood-image"
          />
          <div class="content-overlay"></div>
          <h3 class="seafood-title">{{ banner.name }}</h3>
        </div>
      </div>
    </div>
  </div>
</div>
