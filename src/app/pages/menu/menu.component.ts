import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>f, CommonModule } from '@angular/common';
import { Menu } from '@angular/cdk/menu';
import { CategoryMenu, MenuService, Product, BranchStore } from '../../core/services/menu.service';
import { IconsModule } from '../../icons.module';
import { CurrencyPipe } from '../../pipe/currency.pipe';
import { BookingDialogComponent } from '../../shared/components/booking-dialog/booking-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { DialogHelperService } from '../../shared/services/dialog-helper.service';
import { environment } from '../../../environments/environment';

import { LoadingService } from 'src/app/components/loading/loading.service';
import { finalize } from 'rxjs';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [CommonModule, IconsModule, CurrencyPipe],
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.scss',
})
export class MenuComponent implements OnInit {
  loading = false;
  loadingBranches = false;
  listCategories: CategoryMenu[] = [];
  listProduct: Product[] = [];
  selectedCategoryIndex = -1; // -1 = Tất cả, 0+ = index của category
  branches: BranchStore[] = [];
  imageApiUrl = environment.imageApiUrl + '/';

  // Store ID từ API test
  private readonly storeId = environment.storeId;

  // Anti-spam mechanism for booking button
  isBookingButtonDisabled = false;
  private bookingCooldownTime = 3000; // 3 seconds cooldown
  private lastBookingClickTime = 0;
  constructor(
    private dialog: MatDialog,
    private service: MenuService,
    private dialogHelper: DialogHelperService,
    private loadingService: LoadingService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.getCategories();
    this.getBranchStores();
  }
  getCategories() {
    this.loading = true;
    this.service.getMenuCategories('3').subscribe({
      next: response => {
        console.log('pump', response);

        if (!response.error && response.data) {
          this.listCategories = response.data.categories as CategoryMenu[];
          // Load tất cả sản phẩm khi khởi tạo
          this.getMenuByCategory('all');
        }
        this.loading = false;
      },
      error: error => {
        console.error('Error fetching news:', error);
        this.loading = false;
      },
    });
  }

  getMenuByCategory(pet: string) {
    this.loadingService.show();
    this.service
      .getMenuWithCategory(pet)
      .pipe(finalize(() => this.loadingService.hide()))
      .subscribe({
        next: response => {
          console.log('pump 2', response);

          if (!response.error && response.data) {
            this.listProduct = response.data.result as Product[];
          }
          this.loading = false;
        },
        error: error => {
          console.error('Error fetching news:', error);
          this.loading = false;
        },
      });
  }

  changeCategory(i: number) {
    this.selectedCategoryIndex = i;

    if (i === -1) {
      // Tất cả - không filter theo category
      this.getMenuByCategory('all');
    } else {
      // Filter theo category cụ thể
      const categoryId = this.listCategories[i]._id;
      this.getMenuByCategory(categoryId);
    }
  }

  /**
   * Lấy danh sách cơ sở từ API
   */
  getBranchStores() {
    this.loadingBranches = true;
    this.service.getBranchStores(this.storeId).subscribe({
      next: response => {
        console.log('Branch stores response:', response);

        if (!response.error && response.data) {
          this.branches = response.data.branchStore;
        }
        this.loadingBranches = false;
      },
      error: error => {
        console.error('Error fetching branch stores:', error);
        this.loadingBranches = false;
      },
    });
  }

  /**
   * Mở Google Maps để chỉ đường đến cơ sở
   * @param branch Thông tin cơ sở
   */
  openGoogleMaps(branch: BranchStore) {
    let googleMapsUrl = '';

    // Nếu có tọa độ lat/lng, sử dụng tọa độ
    if (branch.lat && branch.lng) {
      googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${branch.lat},${branch.lng}`;
    }
    // Nếu không có tọa độ, sử dụng địa chỉ
    else if (branch.address) {
      const encodedAddress = encodeURIComponent(branch.address);
      googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
    }

    // Mở Google Maps trong tab mới
    if (googleMapsUrl) {
      window.open(googleMapsUrl, '_blank');
    } else {
      console.error('Không có thông tin địa chỉ hoặc tọa độ để chỉ đường');
    }
  }

  /**
   * Xử lý trường hợp ảnh bị lỗi
   * @param event Event từ DOM
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    // Thay thế bằng ảnh placeholder SVG màu xám #e2e2e2
    imgElement.src = 'assets/images/placeholder.svg';
    // Thêm class để styling riêng nếu cần
    imgElement.classList.add('img-error');
  }
  onBookingClick() {
    // Check if button is currently disabled or in cooldown
    const currentTime = Date.now();

    if (
      this.isBookingButtonDisabled ||
      currentTime - this.lastBookingClickTime < this.bookingCooldownTime
    ) {
      console.log('Booking button is in cooldown, ignoring click');
      return;
    }

    // Update last click time and disable button
    this.lastBookingClickTime = currentTime;
    this.isBookingButtonDisabled = true;

    // Open booking dialog
    this.dialogHelper.openBookingDialog(BookingDialogComponent).subscribe(result => {
      if (result) {
        console.log('Booking created:', result);
        // Có thể thêm logic xử lý sau khi đặt bàn thành công
      }

      // Re-enable button after dialog closes (with minimum cooldown)
      setTimeout(() => {
        this.isBookingButtonDisabled = false;
      }, Math.max(1000, this.bookingCooldownTime - (Date.now() - this.lastBookingClickTime)));
    });

    // Fallback: Re-enable button after cooldown period even if dialog doesn't close properly
    setTimeout(() => {
      this.isBookingButtonDisabled = false;
    }, this.bookingCooldownTime);
  }

  getProductImageUrl(image: string): string {
    if (!image) return 'assets/images/placeholder.svg';
    if (image.startsWith('http')) return image;
    return `${environment.imageApiUrl}/${encodeURIComponent(image)}`;
  }
  decodeHTML(text: string): SafeHtml {
  return this.sanitizer.bypassSecurityTrustHtml(text);
}
}
