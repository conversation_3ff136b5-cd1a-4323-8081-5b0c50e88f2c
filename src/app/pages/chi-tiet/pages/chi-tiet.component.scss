.news-detail {
  padding: 20px 0 40px;
  max-width: 900px;
  margin: 0 auto;
}

.news-header {
  margin-bottom: 1.5rem;
}

.news-title {
  font-weight: 700;
  margin-bottom: 1rem;
  color: #333;
  line-height: 1.3;
}

.news-meta {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  
  i {
    font-size: 0.9rem;
  }
}

.featured-image-container {
  margin-bottom: 2rem;
  border-radius: 8px;
  overflow: hidden;
}

.featured-image {
  width: 100%;
  max-height: 450px;
  object-fit: cover;
}

.news-description {
  font-size: 1.1rem;
  font-weight: 500;
  color: #444;
  margin-bottom: 2rem;
  line-height: 1.6;
  border-left: 4px solid #025b94;
  padding-left: 1rem;
}

.news-content {
  line-height: 1.7;
  color: #333;
  
  img {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 6px;
  }
  
  h2, h3, h4 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  p {
    margin-bottom: 1rem;
  }
  
  ul, ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
  }
  
  a {
    color: #025b94;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  ul, ol {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.5rem;
    }
  }
}

.back-button {
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .news-detail {
    padding: 15px 0 30px;
  }
  
  .news-title {
    font-size: 1.5rem;
  }
  
  .featured-image {
    max-height: 300px;
  }
  
  .news-description {
    font-size: 1rem;
  }
}

/* Related News Section */
.related-news-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.related-news-title {
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
}

.related-news-container {
}

.related-news-items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.related-news-item {
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
}

.related-news-image-container {
  height: 160px;
  overflow: hidden;
  background-color: #f2f2f2; /* Placeholder background */
}

.related-news-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  
  &.img-error {
    object-fit: contain;
    opacity: 0.7;
  }
}

.related-news-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.related-news-item-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #0066cc;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
  
  &:hover {
    text-decoration: underline;
  }
}

.related-news-date {
  color: #666;
  font-size: 0.8rem;
  margin-top: auto;
}
