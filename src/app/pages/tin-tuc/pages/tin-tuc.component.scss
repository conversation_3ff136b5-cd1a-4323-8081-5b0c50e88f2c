.tin-tuc-section {
  margin-top: 50px;
  font-family: var(--font-family, 'Roboto', sans-serif);
  padding: 20px 0 40px;
  max-width: 900px;
  margin: 0 auto;
}

.tin-tuc-label {
  display: inline-block;
  padding: 6px 14px;
  background-color: #3498db;
  color: white;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  .spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 10px;
    }
    
    span {
      color: #666;
      font-size: 14px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.news-list-container {
  padding: 10px 0;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-item {
  display: flex;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
  gap: 20px;
  
  &:last-child {
    border-bottom: none;
  }

  @media (max-width: 767px) {
    flex-direction: column;
  }
}

.news-image-container {
  flex: 0 0 160px;
  height: 120px;
  overflow: hidden;
  background-color: silver; /* Placeholder background khi ảnh bị lỗi */
  
  @media (max-width: 767px) {
    flex: 0 0 auto;
    height: 180px;
    width: 100%;
  }
}

.news-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  
  &.img-error {
    object-fit: contain;    
    opacity: 0.7;
  }
}

.news-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.news-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #0066cc;
  text-decoration: none;
  display: block;
  
  &:hover {
    text-decoration: underline;
  }
}

.news-description {
  color: #333;
  font-size: 14px;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.news-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  margin-top: auto;
  
  .news-date {
    margin-right: 15px;
  }
  
  .news-views {
    display: flex;
    align-items: center;
    
    svg {
      margin-right: 4px;
      width: 14px;
      height: 14px;
    }
  }
}

.empty-news-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50px 20px;
  text-align: center;
  color: #666;
  
  svg {
    margin-bottom: 15px;
    color: #ccc;
  }
  
  p {
    font-size: 1.1rem;
  }
}

@media (max-width: 767px) {
  .tin-tuc-section {
    padding: 15px;
  }
  
  .news-title {
    font-size: 15px;
  }
  
  .news-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .news-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
