#choHaiSanCarousel {
  margin-bottom: 0;

  .carousel-inner {
    height: 500px; // Fixed height as requested
  }

  .carousel-item {
    height: 100%;
  }

  .carousel-image {
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.frame-child,
.frame-inner,
.frame-item {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-100);
  background-color: var(--color-gray-500);
  width: 15px;
  height: 5px;
}
.frame-inner,
.frame-item {
  left: 36px;
  background-color: var(--color-white);
}
.frame-inner {
  left: 18px;
  background-color: var(--color-gray-500);
}
.rectangle-parent {
  height: 5px;
  width: 51px;
  position: relative;
}
.frame-wrapper {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  padding: 0 var(--padding-20) 51px 21px;
}
.ch-hi-sn {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}
.ch-hi-sn-lng-nui-bin-v-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 152px 0 149px;
}
.rectangle-div {
  height: 50px;
  width: 250px;
  position: relative;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-300);
  display: none;
}
.thc-phm-ti {
  position: relative;
  font-size: var(--font-size-14);
  font-family: var(--font-varela);
  color: var(--color-white);
  text-align: left;
  z-index: 1;
}
.frame-child1,
.rectangle-group {
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-300);
}
.rectangle-group {
  cursor: pointer;
  border: 0;
  padding: var(--padding-16) var(--padding-22) var(--padding-16) 23px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.rectangle-group:hover,
.rectangle-parent1:hover {
  background-color: var(--color-steelblue-200);
}
.frame-child1 {
  height: 50px;
  width: 245px;
  position: relative;
  display: none;
}
.frame-button,
.rectangle-container {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.rectangle-container {
  align-self: stretch;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-300);
  flex-direction: row;
  padding: var(--padding-15) 47px 17px;
}
.frame-button {
  cursor: pointer;
  border: 0;
  padding: 0 7px 0 0;
  background-color: transparent;
  flex: 1;
  flex-direction: column;
  box-sizing: border-box;
  min-width: 164px;
}
.frame-child2 {
  height: 50px;
  width: 247px;
  position: relative;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-300);
  display: none;
}
.frame-parent1,
.rectangle-parent1 {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.rectangle-parent1 {
  cursor: pointer;
  border: 0;
  padding: var(--padding-15) 25px 17px var(--padding-30);
  background-color: var(--color-steelblue-300);
  border-radius: var(--br-5);
}
.frame-parent1 {
  align-self: stretch;
  gap: var(--gap-30);
  padding-inline: var(--padding-100);
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  padding: 1rem 0;
}
.frame-container,
.frame-div {
  display: flex;
  align-items: center;
  max-width: 100%;
}
.frame-div {
  flex-direction: column;
  justify-content: center;
  gap: 36px;
}
.frame-container {
  width: 1265px;
  flex-direction: row;
  justify-content: center;
  padding: 0 var(--padding-20);
  box-sizing: border-box;
}
.frame-group,
.frame-parent2 {
  align-self: stretch;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.frame-parent2 {
  flex-direction: row;
  flex-wrap: wrap;
  align-content: flex-start;
}
.frame-group {
  flex-direction: column;
  gap: var(--gap-48);
  max-width: 100%;
}
.frame-child3 {
  height: 50px;
  width: 239px;
  position: relative;
  border-radius: var(--br-5);
  background-color: var(--color-black);
  border: 1px solid var(--color-steelblue-100);
  box-sizing: border-box;
  display: none;
}
.frame-wrapper1,
.rectangle-parent2 {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  box-sizing: border-box;
}
.rectangle-parent2 {
  height: 50px;
  flex: 1;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-300);
  border: 1px solid var(--color-steelblue-100);
  justify-content: flex-start;
  padding: var(--padding-14) 38px;
  &:hover {
    opacity: 0.8;
  }
}
.frame-wrapper1 {
  cursor: pointer;
  border: 0;
  background-color: transparent;
  justify-content: center;
  max-width: 100%;
}
.cho-hai-san-inner,
.frame-parent {
  display: flex;
  justify-content: space-between;
  max-width: 100%;
}
.frame-parent {
  flex: 1;
  flex-direction: column;
  align-items: center;
}
.cho-hai-san-inner {
  align-self: stretch;
  flex-direction: row;
  align-items: flex-start;
  box-sizing: border-box;
  text-align: left;
  font-size: var(--font-size-24);
  color: var(--color-steelblue-300);
  font-family: var(--font-varela);
  padding-bottom: 30px;
}
.image-icon {
  height: 450px;
  position: absolute;
  width: 100vw;
  overflow: hidden;
  object-fit: cover;
  cursor: pointer;
  z-index: 1;
  left: 0;
  top: 0;
}
.image-615-icon {
  width: 100px;
  position: absolute;
  margin: 0 !important;
  top: -44px;
  left: 121px;
  max-height: 100%;
  object-fit: cover;
  z-index: 2;
}
.cho-hai-san,
.product-images {
  width: 100vw;
  flex-direction: row;
  max-width: 100%;
  margin-bottom: 350px;
}
.product-images {
  width: 1440px;
  flex-direction: row;
  max-width: 100%;
}

/* Carousel Styles */
.full-width-carousel {
  width: 100vw;
  margin-bottom: 350px;
  position: relative;

  .carousel-image {
    height: 450px;
    object-fit: cover;
    cursor: pointer;
  }

  // .carousel-indicators {
  //   bottom: 20px;

  //   button {
  //     width: 12px;
  //     height: 12px;
  //     border-radius: 50%;
  //     border: 2px solid rgba(255, 255, 255, 0.8);
  //     background-color: transparent;

  //     &.active {
  //       background-color: rgba(255, 255, 255, 0.9);
  //     }
  //   }
  // }

  .carousel-control-prev,
  .carousel-control-next {
    width: 5%;

    .carousel-control-prev-icon,
    .carousel-control-next-icon {
      width: 30px;
      height: 30px;
      background-size: 100%;
    }
  }

  .carousel-caption {
    bottom: 60px;

    h5 {
      font-size: 1.5rem;
      font-weight: 600;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    }
  }

  .no-banners {
    width: 100%;
    height: 450px;
    display: flex;
    align-items: center;
    justify-content: center;

    .image-icon {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
.cho-hai-san {
  width: 100%;
  position: relative;
  background-color: var(--color-white);
  overflow: hidden;
  flex-direction: column;
  gap: 106px;
  line-height: normal;
  letter-spacing: normal;
}
@media screen and (max-width: 1100px) {
  .ch-hi-sn-lng-nui-bin-v-wrapper {
    padding-left: 74px;
    padding-right: 76px;
    box-sizing: border-box;
  }
}
@media screen and (max-width: 750px) {
  .frame-parent1 {
    flex-wrap: wrap;
    padding-inline: 0;
  }
  .frame-group {
    gap: var(--gap-24);
  }
  .frame-parent {
    gap: 45px;
  }
  .cho-hai-san-inner {
    padding-left: var(--padding-40);
    padding-right: var(--padding-40);
    box-sizing: border-box;
  }
  .cho-hai-san {
    gap: 53px;
  }
}
@media screen and (max-width: 450px) {

  .ch-hi-sn {
    font-size: var(--font-size-19);
  }
  .ch-hi-sn-lng-nui-bin-v-wrapper {
    padding-left: var(--padding-20);
    padding-right: var(--padding-20);
    box-sizing: border-box;
  }
  .frame-div {
    gap: var(--gap-18);
  }
  .frame-wrapper1 {
    padding-left: var(--padding-20);
    padding-right: var(--padding-20);
    box-sizing: border-box;
  }
  .frame-parent {
    gap: var(--gap-22);
  }
  .cho-hai-san {
    gap: 26px;
  }
}

// Custom styles for API integration
// Category button active state
.rectangle-group.active {
  background-color: var(--color-steelblue-100) !important;

  .thc-phm-ti {
    font-weight: bold;
  }
}

// Loading states
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;

  .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #6c757d;

  i {
    margin-bottom: 1rem;
  }
}

// Load more button loading state
.frame-wrapper1:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.125rem;
}

.frame-parent1 {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  padding: 1rem 0;
}

.category-btn {
  //display: inline-flex;
  //min-width: fit-content;
  //margin: 0.5rem;
  //border: none;
  //background: transparent;
  //transition: all 0.3s ease;
  //padding: 0.5rem 1rem;
  //position: relative;

  .rectangle-div {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);
    border-radius: 8px;
    z-index: 0;
  }

  .thc-phm-ti {
    position: relative;
    z-index: 1;
    white-space: nowrap;
    padding: 0 0.5rem;
  }

  &:hover {
    transform: translateY(-2px);
  }

  &.active {
    .rectangle-div {
      background-color: var(--color-primary);
    }
    .thc-phm-ti {
      color: var(--color-white);
    }
  }
}
