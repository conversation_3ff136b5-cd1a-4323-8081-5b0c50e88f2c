import { Component, ViewEncapsulation, HostBinding, OnInit, OnDestroy } from '@angular/core';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import {Subject, Subscription} from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DropdownMenuComponent } from '../../dropdown-menu/dropdown-menu.component';
import { BookingDialogComponent } from '../../shared/components/booking-dialog/booking-dialog.component';
import { DialogHelperService } from '../../shared/services/dialog-helper.service';
import { IconsModule } from '../../icons.module';
import { CartService } from '../../core/services/cart.service';
import {UserModel} from "../../core/models/auth.model";
import {AuthService} from "../../core/services/auth.service";

@Component({
  selector: 'app-navbar',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [DropdownMenuComponent, RouterModule, CommonModule, IconsModule],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';
  isMobileMenuOpen = false;
  cartItemsCount = 0;
  isAuthenticated = false;
  currentUser: UserModel | null = null;
  private authSubscription: Subscription | null = null;

  private destroy$ = new Subject<void>();

  // Anti-spam mechanism for booking button
  isBookingButtonDisabled = false;
  private bookingCooldownTime = 3000; // 3 seconds cooldown
  private lastBookingClickTime = 0;

  constructor(
    private route: ActivatedRoute,
    public router: Router,
    private dialogHelper: DialogHelperService,
    private cartService: CartService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    // Check if URL has booking parameter
    this.route.queryParams.subscribe(params => {
      if (params['booking'] !== undefined) {
        // Auto-trigger booking dialog
        this.onBookingClick();
      }
    });

    // Subscribe to cart changes
    this.cartService.cartSummary$.pipe(takeUntil(this.destroy$)).subscribe(summary => {
      this.cartItemsCount = summary.totalCount;
    });

    this.authSubscription = this.authService.isAuthenticated.subscribe(isAuth => {
      this.isAuthenticated = isAuth;
      if (isAuth) {
        this.currentUser = this.authService.currentUserValue;
      } else {
        this.currentUser = null;
      }
    });

  }


  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method để check active route
  isActiveRoute(route: string): boolean {
    const currentUrl = this.router.url.split('?')[0]; // Loại bỏ query params

    // Xử lý đặc biệt cho trang chủ
    if (route === '/') {
      return currentUrl === '/' || currentUrl === '';
    }

    // Xử lý cho các route khác
    return currentUrl === route || currentUrl.startsWith(route + '/');
  }

  onMENUTextClick() {
    this.router.navigate(['/menu']);
  }

  onBookingClick() {
    // Check if button is currently disabled or in cooldown
    const currentTime = Date.now();

    if (
      this.isBookingButtonDisabled ||
      currentTime - this.lastBookingClickTime < this.bookingCooldownTime
    ) {
      console.log('Booking button is in cooldown, ignoring click');
      return;
    }

    // Update last click time and disable button
    this.lastBookingClickTime = currentTime;
    this.isBookingButtonDisabled = true;

    // Open booking dialog
    this.dialogHelper.openBookingDialog(BookingDialogComponent).subscribe(result => {
      if (result) {
        console.log('Booking created:', result);
        // Có thể thêm logic xử lý sau khi đặt bàn thành công
      }

      // Re-enable button after dialog closes (with minimum cooldown)
      setTimeout(() => {
        this.isBookingButtonDisabled = false;
      }, Math.max(1000, this.bookingCooldownTime - (Date.now() - this.lastBookingClickTime)));
    });

    // Fallback: Re-enable button after cooldown period even if dialog doesn't close properly
    setTimeout(() => {
      this.isBookingButtonDisabled = false;
    }, this.bookingCooldownTime);
  }

  onPayment() {
    this.router.navigate(['/thanh-toan']);
  }

  // Mobile menu functionality
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }
}
