<div class="sticky-top bg-header">
  <div class="container">
    <!-- Desktop menu (hidden on small screens) -->
    <div class="navbar-container d-none d-lg-flex">
      <!-- Logo ở bên trái -->
      <div class="logo-container">
        <img class="logo-white" loading="lazy" alt="Logo" src="assets/<EMAIL>" />
      </div>

      <!-- <PERSON><PERSON> ch<PERSON>h ở giữa -->
      <div class="menu-wrapper">
        <div class="menu" [class.active]="isActiveRoute('/')" routerLink="/">TRANG CHỦ</div>
        <div class="menu" [class.active]="isActiveRoute('/p/gioi-thieu')" routerLink="/p/gioi-thieu">GIỚI THIỆU</div>
        <div class="menu" [class.active]="isActiveRoute('/menu')" (click)="onMENUTextClick()">MENU</div>
        <div class="menu" [class.active]="isActiveRoute('/cho-hai-san')" routerLink="/cho-hai-san">SIÊU THỊ</div>
        <div class="menu" [class.active]="isActiveRoute('/tin-tuc')" routerLink="/tin-tuc">TIN TỨC</div>
      </div>

      <!-- Nút đặt bàn và các icon ở bên phải -->
      <div class="right-menu">
        <button 
          class="frame-wrapper3" 
          [class.disabled]="isBookingButtonDisabled"
          [disabled]="isBookingButtonDisabled"
          (click)="onBookingClick()">
          <div class="rectangle-parent3">
            <div class="frame-child5"></div>
            <img class="image-579-icon" alt="" src="assets/<EMAIL>" />
            <div class="t-bn-ngay">
              <span *ngIf="!isBookingButtonDisabled">ĐẶT BÀN NGAY</span>
              <span *ngIf="isBookingButtonDisabled">VUI LÒNG CHỜ...</span>
            </div>
          </div>
        </button>
        <div class="icons-container">
          <!-- <div class="bell-notification-1-wrapper">
            <img class="bell-notification-1-icon" loading="lazy" alt="" src="assets/bellnotification-1.svg" />
          </div> -->
          <div class="shopping-cart-1-wrapper">
            <div class="cart-icon-container">
              <img class="bell-notification-1-icon" loading="lazy" alt="" src="assets/shopping-cart-1.svg" (click)="onPayment()"/>
              <span class="cart-badge" *ngIf="cartItemsCount > 0">{{ cartItemsCount > 99 ? '99+' : cartItemsCount }}</span>
            </div>
          </div>
        </div>
        <app-dropdown-menu></app-dropdown-menu>
      </div>
    </div>

    <!-- Mobile/Tablet menu (visible only on small screens) -->
    <div class="mobile-navbar d-flex d-lg-none">
      <div class="d-flex justify-content-between align-items-center w-100 py-2">
        <!-- Hamburger menu button (Left) -->
        <div class="mobile-left">
          <button class="btn border-0 p-0" (click)="toggleMobileMenu()">
            <i-tabler name="menu" class="menu-icon"></i-tabler>
          </button>
        </div>

        <!-- Logo (Center) -->
        <div class="logo-container text-center">
          <img class="logo-white" loading="lazy" alt="Logo" src="assets/<EMAIL>" />
        </div>

        <!-- Mobile icons (Right) -->
        <div class="mobile-right d-flex align-items-center">
          <!-- Notification icon -->
          <!-- <div class="me-3">
            <img class="bell-notification-1-icon" loading="lazy" alt="" src="assets/bellnotification-1.svg" />
          </div> -->
          
          <!-- Shopping cart icon -->
          <div>
            <div class="cart-icon-container">
              <img class="bell-notification-1-icon" loading="lazy" alt="" src="assets/shopping-cart-1.svg" (click)="onPayment()"/>
              <span class="cart-badge" *ngIf="cartItemsCount > 0">{{ cartItemsCount > 99 ? '99+' : cartItemsCount }}</span>
            </div>
          </div>
          <app-dropdown-menu></app-dropdown-menu>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Menu Dropdown (Initially hidden) -->
  <div class="mobile-menu" [ngClass]="{'show-mobile-menu': isMobileMenuOpen}">
    <div class="container py-3">
      <div class="mobile-menu-item" [class.active]="isActiveRoute('/')" routerLink="/" (click)="closeMobileMenu()">TRANG CHỦ</div>
      <div class="mobile-menu-item" [class.active]="isActiveRoute('/gioi-thieu')" routerLink="/gioi-thieu" (click)="closeMobileMenu()">GIỚI THIỆU</div>
      <div class="mobile-menu-item" [class.active]="isActiveRoute('/menu')" (click)="onMENUTextClick(); closeMobileMenu()">MENU</div>
      <div class="mobile-menu-item" [class.active]="isActiveRoute('/cho-hai-san')" routerLink="/cho-hai-san" (click)="closeMobileMenu()">SIÊU THỊ</div>
      <div class="mobile-menu-item" [class.active]="isActiveRoute('/tin-tuc')" routerLink="/tin-tuc" (click)="closeMobileMenu()">TIN TỨC</div>
      <div class="mt-3">
        <button 
          class="mobile-booking-btn w-100 py-2" 
          [class.disabled]="isBookingButtonDisabled"
          [disabled]="isBookingButtonDisabled"
          (click)="onBookingClick(); closeMobileMenu()">
          <img class="booking-icon me-2" alt="" src="assets/<EMAIL>" />
          <span *ngIf="!isBookingButtonDisabled">ĐẶT BÀN NGAY</span>
          <span *ngIf="isBookingButtonDisabled">VUI LÒNG CHỜ...</span>
        </button>
      </div>
    </div>
  </div>
</div>
