import { Component, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { MatIcon } from '@angular/material/icon';
import { MatButton } from '@angular/material/button';
import { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';
import { NgClass, NgForOf, NgIf } from '@angular/common';
import { ManufacturerComponent } from '../components/manufacturer/manufacturer.component';
import { TypeVehicleComponent } from '../components/type-vehicle/type-vehicle.component';
import { VehicleFilterComponent } from '../components/vehicle-filter/vehicle-filter.component';
import { Category, Province } from '../model';
import { LayoutService } from '../service/layout.service';
import { LayoutBusinessLogicService } from '../service/layout-business.service';
import { ToastrService } from 'ngx-toastr';
import { MENU_INFO_AUTH, MENU_INFO_NO_AUTH } from '../constants';
import { NavbarComponent } from '../../../components/navbar/navbar.component';
import { CopyrightComponent } from '../../../components/footer/copyright.component';

@Component({
  selector: 'app-layout-container',
  standalone: true,
  imports: [
    RouterOutlet,
    MatIcon,
    ManufacturerComponent,
    MatButton,
    MatMenu,
    MatMenuItem,
    NgForOf,
    TypeVehicleComponent,
    VehicleFilterComponent,
    NgClass,
    MatMenuTrigger,
    NgIf,
    NavbarComponent,
    CopyrightComponent,
  ],
  templateUrl: './layout-container.component.html',
  styleUrl: './layout-container.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class LayoutContainerComponent {
  listProvince: Province[] = [];
  currentProvince?: Province = { name: 'Toàn quốc' };
  favorite: boolean = false;
  params: any;
  isLogin: boolean = false;
  noAuthMenu = MENU_INFO_NO_AUTH;
  authMenu = MENU_INFO_AUTH;

  get isHomeRoute(): boolean {
    return this.router.url === '/' || this.router.url.startsWith('/trang-chu');
  }

  constructor(
    private router: Router,
    private _service: LayoutService,
    private _toastService: ToastrService,
  ) {
    this.isLogin = !!localStorage.getItem('token');
  }

  ngOnInit(): void {}
}
