.dropdown-container {
  position: relative;
  display: inline-block;
  border-radius: 25px;
  padding: 5px 8px;
  border: 1px solid #5a8fb5;
}

.trigger-element {
  cursor: pointer;
}

/* Styling cho container đã có sẵn */
.rectangle-parent20 {
  display: flex;
  align-items: center;
  background-color: transparent;
}

.frame-child25 {
  width: 40px;
  height: 40px;
  background-color: #f3f3f3;
  border-radius: 50%;
}

.image-541-frame {
  display: flex;
  align-items: center;
}

/* Dropdown menu styling */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 240px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  z-index: 1000;
  display: none;
  overflow: hidden;
  transform: translateY(10px);
  opacity: 0;
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.dropdown-menu.show {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.dropdown-user-info {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
  
  .user-name {
    display: block;
    font-weight: 600;
    font-size: 16px;
    color: var(--primary, #005B94);
    margin-bottom: 3px;
  }
  
  .user-phone {
    display: block;
    font-size: 14px;
    color: #666;
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #073a69; /* Màu xanh đậm từ hình */
  font-weight: 500;
  transition: background-color 0.2s ease;
  cursor: pointer;
  text-decoration: none;
  
  &:hover {
    text-decoration: none;
  }
}

.dropdown-item:hover {
  background-color: #f3f3f3;
}

.item-icon {
  margin-right: 15px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
}

.item-text {
  font-size: 16px;
}

.rectangle-parent4 {
  display: flex;
  align-items: center;
  background-color: transparent;
}

@media screen and (max-width: 750px) {
  .dropdown-container {
    padding: 0;
    margin-left: 10px;
    .trigger-element{
    transform: scale(0.7);
  }
  }
}
